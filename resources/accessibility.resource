*** Settings ***
Documentation    Accessibility testing keywords and utilities
Library          SeleniumLibrary
Library          Collections
Library          OperatingSystem
Library          String
Library          ${EXECDIR}${/}resources${/}libraries${/}a11y.py

*** Variables ***
${ACCESSIBILITY_REPORT_DIR}    ${EXECDIR}${/}output${/}accessibility_reports
${WCAG_LEVEL}                  AA
${FAIL_ON_VIOLATIONS}          True

*** Keywords ***
Setup Accessibility Testing
    [Documentation]    Initialize accessibility testing environment
    Create Directory    ${ACCESSIBILITY_REPORT_DIR}

Initialize Global Accessibility Testing
    [Documentation]    Initialize global variables for collecting accessibility violations
    Set Global Variable    ${GLOBAL_VIOLATIONS}    @{EMPTY}
    Set Global Variable    ${GLOBAL_TEST_RESULTS}    &{EMPTY}

Run Accessibility Tests
    [Documentation]    Run a comprehensive accessibility scan using axe-core
    [Arguments]    ${test_name}=${None}    ${context}=${None}    ${options}=${None}    ${fail_on_violations}=${FAIL_ON_VIOLATIONS}

    # Run axe-core scan using the library instance
    ${results}=    Run Accessibility Scan    ${context}    ${options}
    ${violations}=    Get From Dictionary    ${results}    violations
    ${violation_count}=    Get Length    ${violations}
    
    IF    ${violation_count} > 0 and ${fail_on_violations}
        ${error_msg}=    Format Violation Error Message    ${violations}
        Fail    ${test_name} failed due to accessibility violations:\n\n    ${error_msg}
    END

    RETURN    ${violations}

Create Accessibility Report
    [Documentation]    Save accessibility scan results to a JSON file
    [Arguments]    ${results}

    ${timestamp}=    Get Current Date    result_format=%Y%m%d_%H%M%S
    ${report_file}=    Set Variable    ${ACCESSIBILITY_REPORT_DIR}${/}accessibility_report_${timestamp}.json

    Save Accessibility Report    ${report_file}    ${results}

Format Violation Error Message
    [Documentation]    Format a concise error message for test failures
    [Arguments]    ${violations}

    ${violation_count}=    Get Length    ${violations}
    ${error_lines}=    Create List    Found ${violation_count} accessibility violations:

    FOR    ${violation}    IN    @{violations}
        ${rule_id}=    Get From Dictionary    ${violation}    id    default=unknown
        ${impact}=    Get From Dictionary    ${violation}    impact    default=unknown
        ${nodes}=    Get From Dictionary    ${violation}    nodes    default=@{EMPTY}
        ${node_count}=    Get Length    ${nodes}

        ${violation_line}=    Set Variable    - ${rule_id} (${impact}, ${node_count} elements)
        Append To List    ${error_lines}    ${violation_line}
    END

    ${error_msg}=    Evaluate    "\\n".join($error_lines)
    RETURN    ${error_msg}

Check WCAG Compliance
    [Documentation]    Check WCAG compliance at specified level using axe-core
    [Arguments]    ${level}=${WCAG_LEVEL}    ${fail_on_violations}=${FAIL_ON_VIOLATIONS}

    # Run a full accessibility scan and filter for WCAG violations
    ${timestamp}=    Get Current Date    result_format=%Y%m%d_%H%M%S
    ${report_file}=    Set Variable    ${ACCESSIBILITY_REPORT_DIR}${/}wcag_${level}_${timestamp}.json

    ${results}=    Run Accessibility Scan
    ${all_violations}=    Get From Dictionary    ${results}    violations

    # Filter violations by WCAG level tags
    ${wcag_violations}=    Filter WCAG Violations    ${all_violations}    ${level}
    ${violation_count}=    Get Length    ${wcag_violations}

    # Save filtered results
    ${filtered_results}=    Create Dictionary    violations=${wcag_violations}    passes=${results['passes']}    incomplete=${results['incomplete']}    inapplicable=${results['inapplicable']}
    Save Accessibility Report    ${report_file}    ${filtered_results}

    # Log HTML links to accessibility reports
    ${report_name}=    Set Variable    wcag_${level}_compliance

    Log    WCAG ${level} COMPLIANCE CHECK

    IF    ${violation_count} > 0 and ${fail_on_violations}
        ${error_msg}=    Format Violation Error Message    ${wcag_violations}
        Fail    ${error_msg}
    END

    RETURN    ${wcag_violations}

Filter WCAG Violations
    [Documentation]    Filter violations to only include those relevant to specified WCAG level
    [Arguments]    ${violations}    ${level}

    ${filtered_violations}=    Create List
    ${wcag_tag}=    Set Variable    wcag${level.lower()}

    FOR    ${violation}    IN    @{violations}
        ${tags}=    Get From Dictionary    ${violation}    tags    default=@{EMPTY}
        ${has_wcag_tag}=    Evaluate    any(tag.startswith('wcag') and '${level.lower()}' in tag.lower() for tag in $tags)

        IF    ${has_wcag_tag}
            Append To List    ${filtered_violations}    ${violation}
        END
    END

    RETURN    ${filtered_violations}

Accessibility Test Teardown
    [Documentation]    Clean up after accessibility tests

    # Archive old reports (keep last 10)
    ${reports}=    List Files In Directory    ${ACCESSIBILITY_REPORT_DIR}    *.json
    ${report_count}=    Get Length    ${reports}

    IF    ${report_count} > 10
        ${sorted_reports}=    Evaluate    sorted($reports)
        ${reports_to_remove}=    Get Slice From List    ${sorted_reports}    0    ${report_count - 10}
        FOR    ${report}    IN    @{reports_to_remove}
            Remove File    ${ACCESSIBILITY_REPORT_DIR}${/}${report}
        END
    END

Collect Accessibility Violations
    [Documentation]    Collect violations from a test into global variables
    [Arguments]    ${violations}    ${test_name}

    # Add test name prefix to each violation for tracking
    FOR    ${violation}    IN    @{violations}
        ${violation_with_test}=    Copy Dictionary    ${violation}
        Set To Dictionary    ${violation_with_test}    test_name    ${test_name}
        Append To List    ${GLOBAL_VIOLATIONS}    ${violation_with_test}
    END

    # Store test results summary
    ${violation_count}=    Get Length    ${violations}
    Set To Dictionary    ${GLOBAL_TEST_RESULTS}    ${test_name}    ${violation_count}

    Log    Collected ${violation_count} violations from test: ${test_name}

Create Comprehensive Accessibility Report
    [Documentation]    Create a comprehensive accessibility report from all collected violations

    ${total_violations}=    Get Length    ${GLOBAL_VIOLATIONS}
    ${total_tests}=    Get Length    ${GLOBAL_TEST_RESULTS}

    IF    ${total_violations} == 0
        Log    🎉 No accessibility violations found across ${total_tests} tests!
        RETURN
    END

    Log    Creating comprehensive accessibility report with ${total_violations} violations from ${total_tests} tests

    # Create comprehensive results structure
    ${timestamp}=    Get Current Date    result_format=%Y%m%d_%H%M%S
    ${report_file}=    Set Variable    ${ACCESSIBILITY_REPORT_DIR}${/}comprehensive_accessibility_report_${timestamp}.json
    ${html_file}=    Replace String    ${report_file}    .json    .html

    # Group violations by test
    ${violations_by_test}=    Group Violations By Test    ${GLOBAL_VIOLATIONS}

    # Create comprehensive results structure
    ${comprehensive_results}=    Create Dictionary
    ...    violations=${GLOBAL_VIOLATIONS}
    ...    test_summary=${GLOBAL_TEST_RESULTS}
    ...    violations_by_test=${violations_by_test}
    ...    total_violations=${total_violations}
    ...    total_tests=${total_tests}
    ...    timestamp=${timestamp}

    # Save comprehensive report
    Save Comprehensive Accessibility Report    ${report_file}    ${comprehensive_results}

    # Log summary with HTML links
    Log Comprehensive Accessibility Summary    ${comprehensive_results}    ${report_file}

    # Fail the suite if there are violations
    IF    ${total_violations} > 0
        ${error_msg}=    Format Comprehensive Error Message    ${comprehensive_results}
        Fail    ${error_msg}
    END

Group Violations By Test
    [Documentation]    Group violations by test name for better organization
    [Arguments]    ${all_violations}

    ${violations_by_test}=    Create Dictionary

    FOR    ${violation}    IN    @{all_violations}
        ${test_name}=    Get From Dictionary    ${violation}    test_name    default=Unknown Test

        ${test_violations}=    Get From Dictionary    ${violations_by_test}    ${test_name}    default=@{EMPTY}
        Append To List    ${test_violations}    ${violation}
        Set To Dictionary    ${violations_by_test}    ${test_name}    ${test_violations}
    END

    RETURN    ${violations_by_test}

Save Comprehensive Accessibility Report
    [Documentation]    Save comprehensive accessibility report in JSON and HTML formats
    [Arguments]    ${report_file}    ${comprehensive_results}

    # Save JSON report
    ${json_content}=    Evaluate    json.dumps($comprehensive_results, indent=2, default=str)    modules=json
    Create File    ${report_file}    ${json_content}

    # Generate and save HTML report
    ${html_file}=    Replace String    ${report_file}    .json    .html
    Generate Comprehensive HTML Report    ${html_file}    ${comprehensive_results}

    Log    Comprehensive accessibility reports saved:
    Log    - JSON: ${report_file}
    Log    - HTML: ${html_file}

Generate Comprehensive HTML Report
    [Documentation]    Generate comprehensive HTML report for all accessibility violations
    [Arguments]    ${html_file}    ${comprehensive_results}

    ${total_violations}=    Get From Dictionary    ${comprehensive_results}    total_violations
    ${total_tests}=    Get From Dictionary    ${comprehensive_results}    total_tests
    ${test_summary}=    Get From Dictionary    ${comprehensive_results}    test_summary
    ${violations_by_test}=    Get From Dictionary    ${comprehensive_results}    violations_by_test
    ${timestamp}=    Get From Dictionary    ${comprehensive_results}    timestamp

    # Generate HTML content
    ${html_content}=    Set Variable    <!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>Comprehensive Accessibility Report</title><style>body{font-family:Arial,sans-serif;margin:20px;line-height:1.6}.header{background:#f4f4f4;padding:20px;border-radius:5px;margin-bottom:20px}.summary{display:flex;gap:20px;margin-bottom:30px}.summary-card{background:white;border:1px solid #ddd;padding:15px;border-radius:5px;flex:1;text-align:center}.summary-card.violations{border-color:#d32f2f}.summary-card.tests{border-color:#1976d2}.test-section{background:#fff;border:1px solid #ddd;margin-bottom:20px;border-radius:5px}.test-header{background:#f5f5f5;padding:15px;border-bottom:1px solid #ddd}.violation{background:#fff;border:1px solid #d32f2f;margin:10px 0;border-radius:5px}.violation-header{background:#d32f2f;color:white;padding:10px}.violation-content{padding:15px}.impact{display:inline-block;padding:3px 8px;border-radius:3px;color:white;font-size:12px}.impact.critical{background:#d32f2f}.impact.serious{background:#f57c00}.impact.moderate{background:#1976d2}.impact.minor{background:#388e3c}.element{background:#f9f9f9;border:1px solid #ddd;margin:10px 0;padding:10px;border-radius:3px}.code{background:#f5f5f5;padding:5px;border-radius:3px;font-family:monospace;font-size:12px}</style></head><body>
    ${html_content}=    Set Variable    ${html_content}<div class="header"><h1>Comprehensive Accessibility Report</h1><p>Generated: ${timestamp}</p></div>
    ${html_content}=    Set Variable    ${html_content}<div class="summary"><div class="summary-card violations"><h3>${total_violations}</h3><p>Total Violations</p></div><div class="summary-card tests"><h3>${total_tests}</h3><p>Tests Executed</p></div></div>

    # Add test summary
    ${html_content}=    Set Variable    ${html_content}<h2>Test Summary</h2>
    FOR    ${test_name}    IN    @{test_summary.keys()}
        ${violation_count}=    Get From Dictionary    ${test_summary}    ${test_name}
        ${status_color}=    Set Variable If    ${violation_count} > 0    #d32f2f    #388e3c
        ${status_icon}=    Set Variable If    ${violation_count} > 0    ❌    ✅
        ${html_content}=    Set Variable    ${html_content}<div class="test-section"><div class="test-header" style="color: ${status_color}"><h3>${status_icon} ${test_name}</h3><p>${violation_count} violations found</p></div></div>
    END

    # Add detailed violations by test
    ${html_content}=    Set Variable    ${html_content}<h2>Detailed Violations</h2>
    FOR    ${test_name}    IN    @{violations_by_test.keys()}
        ${test_violations}=    Get From Dictionary    ${violations_by_test}    ${test_name}
        ${test_violation_count}=    Get Length    ${test_violations}
        ${html_content}=    Set Variable    ${html_content}<div class="test-section"><div class="test-header"><h3>${test_name}</h3><p>${test_violation_count} violations</p></div>

        FOR    ${index}    ${violation}    IN ENUMERATE    @{test_violations}    start=1
            ${rule_id}=    Get From Dictionary    ${violation}    id    default=unknown
            ${description}=    Get From Dictionary    ${violation}    description    default=No description
            ${impact}=    Get From Dictionary    ${violation}    impact    default=unknown
            ${help_text}=    Get From Dictionary    ${violation}    help    default=No help available
            ${help_url}=    Get From Dictionary    ${violation}    helpUrl    default=${EMPTY}
            ${nodes}=    Get From Dictionary    ${violation}    nodes    default=@{EMPTY}
            ${node_count}=    Get Length    ${nodes}

            ${html_content}=    Set Variable    ${html_content}<div class="violation"><div class="violation-header"><h4>${index}. ${rule_id}</h4><span class="impact ${impact}">${impact.upper()}</span></div><div class="violation-content"><p><strong>Description:</strong> ${description}</p><p><strong>Help:</strong> ${help_text}</p>

            IF    "${help_url}" != "${EMPTY}"
                ${html_content}=    Set Variable    ${html_content}<p><strong>More info:</strong> <a href="${help_url}" target="_blank">${help_url}</a></p>
            END

            ${html_content}=    Set Variable    ${html_content}<h5>Affected Elements (${node_count}):</h5>
            FOR    ${node_index}    ${node}    IN ENUMERATE    @{nodes}    start=1
                ${target}=    Get From Dictionary    ${node}    target    default=@{EMPTY}
                ${html_snippet}=    Get From Dictionary    ${node}    html    default=No HTML available
                ${failure_summary}=    Get From Dictionary    ${node}    failureSummary    default=No failure summary
                ${target_length}=    Get Length    ${target}
                ${target_selector}=    Set Variable If    ${target_length} > 0    ${target[0]}    unknown

                ${html_content}=    Set Variable    ${html_content}<div class="element"><p><strong>Element ${node_index}:</strong> ${target_selector}</p><p><strong>Issue:</strong> ${failure_summary}</p><div class="code">${html_snippet}</div></div>
            END

            ${html_content}=    Set Variable    ${html_content}</div></div>
        END

        ${html_content}=    Set Variable    ${html_content}</div>
    END

    ${html_content}=    Set Variable    ${html_content}</body></html>

    Create File    ${html_file}    ${html_content}

Log Comprehensive Accessibility Summary
    [Documentation]    Log comprehensive accessibility summary with HTML links
    [Arguments]    ${comprehensive_results}    ${report_file}

    ${total_violations}=    Get From Dictionary    ${comprehensive_results}    total_violations
    ${total_tests}=    Get From Dictionary    ${comprehensive_results}    total_tests
    ${test_summary}=    Get From Dictionary    ${comprehensive_results}    test_summary

    ${html_file}=    Replace String    ${report_file}    .json    .html
    ${relative_json}=    Get Relative Path    ${EXECDIR}    ${report_file}
    ${relative_html}=    Get Relative Path    ${EXECDIR}    ${html_file}
    ${timestamp}=    Get Current Date    result_format=%Y-%m-%d %H:%M:%S

    # Create HTML content for the log
    ${html_content}=    Set Variable    <div style="border: 2px solid #d32f2f; border-radius: 8px; padding: 20px; margin: 15px 0; background: #fff5f5;">
    ${html_content}=    Set Variable    ${html_content}<h2 style="margin: 0 0 15px 0; color: #d32f2f;">📊 Comprehensive Accessibility Report</h2>
    ${html_content}=    Set Variable    ${html_content}<div style="display: flex; gap: 20px; margin: 15px 0;">
    ${html_content}=    Set Variable    ${html_content}<div style="background: #f5f5f5; padding: 15px; border-radius: 5px; text-align: center; flex: 1;">
    ${html_content}=    Set Variable    ${html_content}<h3 style="margin: 0; color: #d32f2f;">${total_violations}</h3><p style="margin: 5px 0; color: #666;">Total Violations</p></div>
    ${html_content}=    Set Variable    ${html_content}<div style="background: #f5f5f5; padding: 15px; border-radius: 5px; text-align: center; flex: 1;">
    ${html_content}=    Set Variable    ${html_content}<h3 style="margin: 0; color: #1976d2;">${total_tests}</h3><p style="margin: 5px 0; color: #666;">Tests Executed</p></div></div>

    # Add test breakdown
    ${html_content}=    Set Variable    ${html_content}<h3 style="color: #333; margin: 15px 0 10px 0;">Test Breakdown:</h3><ul style="margin: 0; padding-left: 20px;">
    FOR    ${test_name}    IN    @{test_summary.keys()}
        ${violation_count}=    Get From Dictionary    ${test_summary}    ${test_name}
        ${status_icon}=    Set Variable If    ${violation_count} > 0    ❌    ✅
        ${html_content}=    Set Variable    ${html_content}<li style="margin: 5px 0; color: #333;"><strong>${test_name}:</strong> ${status_icon} ${violation_count} violations</li>
    END
    ${html_content}=    Set Variable    ${html_content}</ul>

    ${html_content}=    Set Variable    ${html_content}<p style="margin: 15px 0 5px 0; color: #666; font-size: 0.9em;">Generated: ${timestamp}</p>
    ${html_content}=    Set Variable    ${html_content}<div style="margin: 15px 0;">
    ${html_content}=    Set Variable    ${html_content}<a href="${relative_html}" target="_blank" style="display: inline-block; padding: 10px 20px; background: #d32f2f; color: white; text-decoration: none; border-radius: 5px; margin-right: 10px;">📊 View Comprehensive HTML Report</a>
    ${html_content}=    Set Variable    ${html_content}<a href="${relative_json}" target="_blank" style="display: inline-block; padding: 10px 20px; background: #666; color: white; text-decoration: none; border-radius: 5px;">📄 View JSON Data</a>
    ${html_content}=    Set Variable    ${html_content}</div></div>

    # Log the HTML content
    Log    ${html_content}    HTML

Format Comprehensive Error Message
    [Documentation]    Format error message for comprehensive accessibility report
    [Arguments]    ${comprehensive_results}

    ${total_violations}=    Get From Dictionary    ${comprehensive_results}    total_violations
    ${total_tests}=    Get From Dictionary    ${comprehensive_results}    total_tests
    ${test_summary}=    Get From Dictionary    ${comprehensive_results}    test_summary

    ${error_lines}=    Create List    Comprehensive Accessibility Test Failed!
    Append To List    ${error_lines}    Found ${total_violations} total violations across ${total_tests} tests:
    Append To List    ${error_lines}    ${EMPTY}

    FOR    ${test_name}    IN    @{test_summary.keys()}
        ${violation_count}=    Get From Dictionary    ${test_summary}    ${test_name}
        IF    ${violation_count} > 0
            ${test_line}=    Set Variable    - ${test_name}: ${violation_count} violations
            Append To List    ${error_lines}    ${test_line}
        END
    END

    ${error_msg}=    Evaluate    "\\n".join($error_lines)
    RETURN    ${error_msg}

Get Relative Path
    [Documentation]    Get relative path from base to target
    [Arguments]    ${base_path}    ${target_path}

    ${relative_path}=    Evaluate    os.path.relpath('${target_path}', '${base_path}')    modules=os
    RETURN    ${relative_path}
