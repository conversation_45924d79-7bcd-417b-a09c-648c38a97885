*** Settings ***
Documentation    Accessibility testing keywords and utilities
Library          SeleniumLibrary
Library          Collections
Library          OperatingSystem
Library          String
Library          ${EXECDIR}${/}resources${/}libraries${/}a11y.py

*** Variables ***
${ACCESSIBILITY_REPORT_DIR}    ${EXECDIR}${/}output${/}accessibility_reports
${WCAG_LEVEL}                  AA
${FAIL_ON_VIOLATIONS}          True

*** Keywords ***
Setup Accessibility Testing
    [Documentation]    Initialize accessibility testing environment
    Create Directory    ${ACCESSIBILITY_REPORT_DIR}

Run All Accessibility Tests
    [Documentation]    Run a comprehensive accessibility scan using axe-core
    [Arguments]    ${report_name}=accessibility_report    ${fail_on_violations}=${FAIL_ON_VIOLATIONS}

    ${timestamp}=    Get Current Date    result_format=%Y%m%d_%H%M%S
    ${report_file}=    Set Variable    ${ACCESSIBILITY_REPORT_DIR}${/}${report_name}_${timestamp}.json

    # Run axe-core scan using the library instance
    ${results}=    Run Accessibility Scan
    ${violations}=    Get From Dictionary    ${results}    violations

    # Save report using the library instance
    Save Accessibility Report    ${report_file}    ${results}

    # Log detailed summary with better formatting
    ${violation_count}=    Get Length    ${violations}
    Log Accessibility Results Summary    ${results}    ${report_file}

    IF    ${violation_count} > 0 and ${fail_on_violations}
        ${error_msg}=    Format Violation Error Message    ${violations}
        Fail    ${error_msg}
    END

    RETURN    ${violations}

Log Accessibility Results Summary
    [Documentation]    Log a formatted summary of accessibility test results
    [Arguments]    ${results}    ${report_file}

    ${violations}=    Get From Dictionary    ${results}    violations
    ${passes}=    Get From Dictionary    ${results}    passes
    ${incomplete}=    Get From Dictionary    ${results}    incomplete
    ${inapplicable}=    Get From Dictionary    ${results}    inapplicable

    ${violation_count}=    Get Length    ${violations}
    ${passes_count}=    Get Length    ${passes}
    ${incomplete_count}=    Get Length    ${incomplete}
    ${inapplicable_count}=    Get Length    ${inapplicable}

    ${separator}=    Set Variable    ================================================================================
    ${html_file}=    Replace String    ${report_file}    .json    .html

    Log    \n${separator}
    Log    ACCESSIBILITY TEST RESULTS SUMMARY
    Log    ${separator}
    Log    ✓ Passed rules: ${passes_count}
    Log    ⚠ Incomplete rules: ${incomplete_count}
    Log    ✗ Violations: ${violation_count}
    Log    - Inapplicable rules: ${inapplicable_count}
    Log    \nReports saved to:
    Log    - JSON: ${report_file}
    Log    - HTML: ${html_file}
    Log    ${separator}\n

    IF    ${violation_count} > 0
        Log Violation Details    ${violations}
    END

Log Violation Details
    [Documentation]    Log detailed information about accessibility violations
    [Arguments]    ${violations}

    ${separator}=    Set Variable    ================================================================================

    Log    \n${separator}
    Log    DETAILED VIOLATION INFORMATION
    Log    ${separator}

    FOR    ${index}    ${violation}    IN ENUMERATE    @{violations}    start=1
        ${rule_id}=    Get From Dictionary    ${violation}    id    default=unknown
        ${description}=    Get From Dictionary    ${violation}    description    default=No description
        ${impact}=    Get From Dictionary    ${violation}    impact    default=unknown
        ${help_text}=    Get From Dictionary    ${violation}    help    default=No help available
        ${help_url}=    Get From Dictionary    ${violation}    helpUrl    default=${EMPTY}
        ${nodes}=    Get From Dictionary    ${violation}    nodes    default=@{EMPTY}
        ${node_count}=    Get Length    ${nodes}

        ${rule_id_upper}=    Convert To Upper Case    ${rule_id}
        ${impact_upper}=    Convert To Upper Case    ${impact}

        Log    \n${index}. ${rule_id_upper}
        Log    Impact: ${impact_upper}
        Log    Description: ${description}
        Log    Help: ${help_text}
        IF    "${help_url}" != "${EMPTY}"
            Log    More info: ${help_url}
        END
        Log    Affected elements: ${node_count}

        # Log details for first few elements
        ${max_elements}=    Set Variable    ${3}
        ${elements_to_show}=    Evaluate    min(${node_count}, ${max_elements})

        FOR    ${node_index}    IN RANGE    ${elements_to_show}
            ${node}=    Get From List    ${nodes}    ${node_index}
            ${target}=    Get From Dictionary    ${node}    target    default=@{EMPTY}
            ${html}=    Get From Dictionary    ${node}    html    default=No HTML available
            ${failure_summary}=    Get From Dictionary    ${node}    failureSummary    default=No failure summary

            ${target_length}=    Get Length    ${target}
            ${target_selector}=    Set Variable If    ${target_length} > 0    ${target[0]}    unknown
            ${html_length}=    Get Length    ${html}
            ${truncated_html}=    Set Variable If    ${html_length} > 100    ${html[:100]}...    ${html}
            ${element_number}=    Evaluate    ${node_index} + 1

            Log    Element ${element_number}: ${target_selector}
            Log    HTML: ${truncated_html}
            Log    Issue: ${failure_summary}
        END

        IF    ${node_count} > ${max_elements}
            ${remaining}=    Evaluate    ${node_count} - ${max_elements}
            Log    ... and ${remaining} more elements
        END
    END

    Log    ${separator}\n

Format Violation Error Message
    [Documentation]    Format a concise error message for test failures
    [Arguments]    ${violations}

    ${violation_count}=    Get Length    ${violations}
    ${error_lines}=    Create List    Found ${violation_count} accessibility violations:

    FOR    ${violation}    IN    @{violations}
        ${rule_id}=    Get From Dictionary    ${violation}    id    default=unknown
        ${impact}=    Get From Dictionary    ${violation}    impact    default=unknown
        ${nodes}=    Get From Dictionary    ${violation}    nodes    default=@{EMPTY}
        ${node_count}=    Get Length    ${nodes}

        ${violation_line}=    Set Variable    - ${rule_id} (${impact}, ${node_count} elements)
        Append To List    ${error_lines}    ${violation_line}
    END

    ${error_msg}=    Evaluate    "\\n".join($error_lines)
    RETURN    ${error_msg}

Check WCAG Compliance
    [Documentation]    Check WCAG compliance at specified level using axe-core
    [Arguments]    ${level}=${WCAG_LEVEL}    ${fail_on_violations}=${FAIL_ON_VIOLATIONS}

    # Run a full accessibility scan and filter for WCAG violations
    ${timestamp}=    Get Current Date    result_format=%Y%m%d_%H%M%S
    ${report_file}=    Set Variable    ${ACCESSIBILITY_REPORT_DIR}${/}wcag_${level}_${timestamp}.json

    ${results}=    Run Accessibility Scan
    ${all_violations}=    Get From Dictionary    ${results}    violations

    # Filter violations by WCAG level tags
    ${wcag_violations}=    Filter WCAG Violations    ${all_violations}    ${level}
    ${violation_count}=    Get Length    ${wcag_violations}

    # Save filtered results
    ${filtered_results}=    Create Dictionary    violations=${wcag_violations}    passes=${results['passes']}    incomplete=${results['incomplete']}    inapplicable=${results['inapplicable']}
    Save Accessibility Report    ${report_file}    ${filtered_results}

    ${separator}=    Set Variable    ================================================================================

    Log    \n${separator}
    Log    WCAG ${level} COMPLIANCE CHECK
    Log    ${separator}

    IF    ${violation_count} > 0
        Log    ❌ WCAG ${level} violations found: ${violation_count}
        Log Violation Details    ${wcag_violations}

        IF    ${fail_on_violations}
            ${error_msg}=    Format Violation Error Message    ${wcag_violations}
            Fail    WCAG ${level} compliance failed:\n${error_msg}
        END
    ELSE
        Log    ✅ No WCAG ${level} violations found - Compliance check passed!
    END

    Log    ${separator}\n
    RETURN    ${wcag_violations}

Filter WCAG Violations
    [Documentation]    Filter violations to only include those relevant to specified WCAG level
    [Arguments]    ${violations}    ${level}

    ${filtered_violations}=    Create List
    ${wcag_tag}=    Set Variable    wcag${level.lower()}

    FOR    ${violation}    IN    @{violations}
        ${tags}=    Get From Dictionary    ${violation}    tags    default=@{EMPTY}
        ${has_wcag_tag}=    Evaluate    any(tag.startswith('wcag') and '${level.lower()}' in tag.lower() for tag in $tags)

        IF    ${has_wcag_tag}
            Append To List    ${filtered_violations}    ${violation}
        END
    END

    RETURN    ${filtered_violations}

Accessibility Test Teardown
    [Documentation]    Clean up after accessibility tests
    
    # Archive old reports (keep last 10)
    ${reports}=    List Files In Directory    ${ACCESSIBILITY_REPORT_DIR}    *.json
    ${report_count}=    Get Length    ${reports}
    
    IF    ${report_count} > 10
        ${sorted_reports}=    Evaluate    sorted($reports)
        ${reports_to_remove}=    Get Slice From List    ${sorted_reports}    0    ${report_count - 10}
        FOR    ${report}    IN    @{reports_to_remove}
            Remove File    ${ACCESSIBILITY_REPORT_DIR}${/}${report}
        END
    END
