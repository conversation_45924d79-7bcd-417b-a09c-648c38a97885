*** Settings ***
Documentation    Accessibility testing keywords and utilities
Library          SeleniumLibrary
Library          Collections
Library          OperatingSystem
Library          String
Library          ${EXECDIR}${/}resources${/}libraries${/}a11y.py

*** Variables ***
${ACCESSIBILITY_REPORT_DIR}    ${EXECDIR}${/}output${/}accessibility_reports
${WCAG_LEVEL}                  AA
${FAIL_ON_VIOLATIONS}          True

*** Keywords ***
Setup Accessibility Testing
    [Documentation]    Initialize accessibility testing environment
    Create Directory    ${ACCESSIBILITY_REPORT_DIR}

Run All Accessibility Tests
    [Documentation]    Run a comprehensive accessibility scan using axe-core
    [Arguments]    ${report_name}=accessibility_report    ${fail_on_violations}=${FAIL_ON_VIOLATIONS}

    ${timestamp}=    Get Current Date    result_format=%Y%m%d_%H%M%S
    ${report_file}=    Set Variable    ${ACCESSIBILITY_REPORT_DIR}${/}${report_name}_${timestamp}.json

    # Run axe-core scan using the library instance
    ${results}=    Run Accessibility Scan
    ${violations}=    Get From Dictionary    ${results}    violations

    # Save report using the library instance
    Save Accessibility Report    ${report_file}    ${results}

    # Log summary
    ${violation_count}=    Get Length    ${violations}
    Log    Found ${violation_count} accessibility violations

    IF    ${violation_count} > 0 and ${fail_on_violations}
        Fail    Found ${violation_count} accessibility violations
    END

    RETURN    ${violations}

Check WCAG Compliance
    [Documentation]    Check WCAG compliance at specified level using axe-core
    [Arguments]    ${level}=${WCAG_LEVEL}    ${fail_on_violations}=${FAIL_ON_VIOLATIONS}

    # Call the Python library method directly using the library instance
    ${violations}=    Check WCAG Compliance    level=${level}    fail_on_violations=False
    ${violation_count}=    Get Length    ${violations}

    IF    ${violation_count} > 0
        Log    WCAG ${level} violations found: ${violation_count}
        FOR    ${violation}    IN    @{violations}
            ${rule_id}=    Get From Dictionary    ${violation}    id    default=unknown
            ${description}=    Get From Dictionary    ${violation}    description    default=No description
            Log    Violation: ${rule_id} - ${description}
        END

        IF    ${fail_on_violations}
            Fail    Found ${violation_count} WCAG ${level} violations
        END
    ELSE
        Log    No WCAG ${level} violations found
    END

    RETURN    ${violations}

Accessibility Test Teardown
    [Documentation]    Clean up after accessibility tests
    
    # Archive old reports (keep last 10)
    ${reports}=    List Files In Directory    ${ACCESSIBILITY_REPORT_DIR}    *.json
    ${report_count}=    Get Length    ${reports}
    
    IF    ${report_count} > 10
        ${sorted_reports}=    Evaluate    sorted($reports)
        ${reports_to_remove}=    Get Slice From List    ${sorted_reports}    0    ${report_count - 10}
        FOR    ${report}    IN    @{reports_to_remove}
            Remove File    ${ACCESSIBILITY_REPORT_DIR}${/}${report}
        END
    END
