"""
Accessibility testing library for Robot Framework using axe-core
"""
import json
from robot.api.deco import keyword, not_keyword
from robot.libraries.BuiltIn import BuiltIn
from axe_selenium_python import Axe

def run_accessibility_scan():
  print("Running accessibility scan")

  selenium_lib = BuiltIn().get_library_instance("SeleniumLibrary")
  driver = selenium_lib.driver
  axe = Axe(driver)
  axe.inject()
  results = axe.run()
  print(results)
  return results

def save_accessibility_report(report_file, results):
  print(f"Saving accessibility report to {report_file}")
  with open(report_file, "w") as f:
    json.dump(results, f)
