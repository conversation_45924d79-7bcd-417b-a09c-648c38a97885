"""
Accessibility testing library for Robot Framework using axe-core
"""
import json
from datetime import datetime
from robot.libraries.BuiltIn import BuiltIn
from axe_selenium_python import Axe

def run_accessibility_scan():
    """Run accessibility scan and return formatted results"""
    print("Running accessibility scan...")

    selenium_lib = BuiltIn().get_library_instance("SeleniumLibrary")
    driver = selenium_lib.driver
    axe = Axe(driver)
    axe.inject()
    results = axe.run()

    # Log formatted summary instead of raw JSON
    _log_accessibility_summary(results)

    return results

def save_accessibility_report(report_file, results):
    """Save accessibility report in both JSON and HTML formats"""
    print(f"Saving accessibility report to {report_file}")

    # Save JSON report
    with open(report_file, "w") as f:
        json.dump(results, f, indent=2)

    # Generate and save HTML report
    html_file = report_file.replace('.json', '.html')
    _generate_html_report(html_file, results)
    print(f"HTML report saved to {html_file}")

def _log_accessibility_summary(results):
    """Log a formatted summary of accessibility results"""
    violations = results.get('violations', [])
    passes = results.get('passes', [])
    incomplete = results.get('incomplete', [])
    inapplicable = results.get('inapplicable', [])

    print("\n" + "="*80)
    print("ACCESSIBILITY SCAN RESULTS")
    print("="*80)
    print(f"✓ Passed rules: {len(passes)}")
    print(f"⚠ Incomplete rules: {len(incomplete)}")
    print(f"✗ Violations: {len(violations)}")
    print(f"- Inapplicable rules: {len(inapplicable)}")

    if violations:
        print("\n" + "-"*80)
        print("VIOLATIONS FOUND:")
        print("-"*80)

        for i, violation in enumerate(violations, 1):
            _log_violation_details(violation, i)

    print("="*80 + "\n")

def _log_violation_details(violation, index):
    """Log detailed information about a single violation"""
    rule_id = violation.get('id', 'unknown')
    description = violation.get('description', 'No description available')
    help_text = violation.get('help', 'No help available')
    help_url = violation.get('helpUrl', '')
    impact = violation.get('impact', 'unknown')
    tags = violation.get('tags', [])
    nodes = violation.get('nodes', [])

    print(f"\n{index}. {rule_id.upper()}")
    print(f"   Impact: {impact.upper()}")
    print(f"   Description: {description}")
    print(f"   Help: {help_text}")
    if help_url:
        print(f"   More info: {help_url}")
    if tags:
        print(f"   Tags: {', '.join(tags)}")

    print(f"   Affected elements ({len(nodes)}):")
    for j, node in enumerate(nodes[:5], 1):  # Show max 5 elements
        target = node.get('target', ['unknown'])
        html = node.get('html', 'No HTML available')
        failure_summary = node.get('failureSummary', 'No failure summary')

        print(f"     {j}. Target: {target[0] if target else 'unknown'}")
        print(f"        HTML: {html[:100]}{'...' if len(html) > 100 else ''}")
        print(f"        Issue: {failure_summary}")

    if len(nodes) > 5:
        print(f"     ... and {len(nodes) - 5} more elements")

def _generate_html_report(html_file, results):
    """Generate an HTML report for accessibility results"""
    violations = results.get('violations', [])
    passes = results.get('passes', [])
    incomplete = results.get('incomplete', [])

    html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accessibility Test Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }}
        .header {{ background: #f4f4f4; padding: 20px; border-radius: 5px; margin-bottom: 20px; }}
        .summary {{ display: flex; gap: 20px; margin-bottom: 30px; }}
        .summary-card {{ background: white; border: 1px solid #ddd; padding: 15px; border-radius: 5px; flex: 1; text-align: center; }}
        .summary-card.violations {{ border-color: #d32f2f; }}
        .summary-card.passes {{ border-color: #388e3c; }}
        .summary-card.incomplete {{ border-color: #f57c00; }}
        .violation {{ background: #fff; border: 1px solid #d32f2f; margin-bottom: 20px; border-radius: 5px; }}
        .violation-header {{ background: #d32f2f; color: white; padding: 15px; }}
        .violation-content {{ padding: 15px; }}
        .impact {{ display: inline-block; padding: 3px 8px; border-radius: 3px; color: white; font-size: 12px; }}
        .impact.critical {{ background: #d32f2f; }}
        .impact.serious {{ background: #f57c00; }}
        .impact.moderate {{ background: #1976d2; }}
        .impact.minor {{ background: #388e3c; }}
        .element {{ background: #f9f9f9; border: 1px solid #ddd; margin: 10px 0; padding: 10px; border-radius: 3px; }}
        .code {{ background: #f5f5f5; padding: 5px; border-radius: 3px; font-family: monospace; font-size: 12px; }}
        .tags {{ margin: 10px 0; }}
        .tag {{ background: #e0e0e0; padding: 2px 6px; border-radius: 3px; font-size: 11px; margin-right: 5px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>Accessibility Test Report</h1>
        <p>Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>

    <div class="summary">
        <div class="summary-card violations">
            <h3>{len(violations)}</h3>
            <p>Violations</p>
        </div>
        <div class="summary-card passes">
            <h3>{len(passes)}</h3>
            <p>Passed Rules</p>
        </div>
        <div class="summary-card incomplete">
            <h3>{len(incomplete)}</h3>
            <p>Incomplete Rules</p>
        </div>
    </div>
"""

    if violations:
        html_content += "<h2>Violations</h2>\n"
        for i, violation in enumerate(violations, 1):
            html_content += _generate_violation_html(violation, i)
    else:
        html_content += "<h2>🎉 No Violations Found!</h2>\n<p>All accessibility tests passed.</p>\n"

    html_content += """
</body>
</html>
"""

    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(html_content)

def _generate_violation_html(violation, index):
    """Generate HTML for a single violation"""
    rule_id = violation.get('id', 'unknown')
    description = violation.get('description', 'No description available')
    help_text = violation.get('help', 'No help available')
    help_url = violation.get('helpUrl', '')
    impact = violation.get('impact', 'unknown')
    tags = violation.get('tags', [])
    nodes = violation.get('nodes', [])

    html = f"""
    <div class="violation">
        <div class="violation-header">
            <h3>{index}. {rule_id}</h3>
            <span class="impact {impact}">{impact.upper()}</span>
        </div>
        <div class="violation-content">
            <p><strong>Description:</strong> {description}</p>
            <p><strong>Help:</strong> {help_text}</p>
"""

    if help_url:
        html += f'            <p><strong>More info:</strong> <a href="{help_url}" target="_blank">{help_url}</a></p>\n'

    if tags:
        html += '            <div class="tags">\n'
        for tag in tags:
            html += f'                <span class="tag">{tag}</span>\n'
        html += '            </div>\n'

    html += f'            <h4>Affected Elements ({len(nodes)}):</h4>\n'

    for j, node in enumerate(nodes, 1):
        target = node.get('target', ['unknown'])
        html_snippet = node.get('html', 'No HTML available')
        failure_summary = node.get('failureSummary', 'No failure summary')

        html += f"""
            <div class="element">
                <p><strong>Element {j}:</strong> {target[0] if target else 'unknown'}</p>
                <p><strong>Issue:</strong> {failure_summary}</p>
                <div class="code">{html_snippet}</div>
            </div>
"""

    html += """
        </div>
    </div>
"""

    return html
