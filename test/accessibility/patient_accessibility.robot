*** Settings ***
Documentation       Accessibility tests for patient app
...                 Tests WCAG 2.1 AA compliance across patient workflows

Resource            ${EXECDIR}${/}resources${/}accessibility.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}cookies.resource

Suite Setup         Run Keywords    Set Libraries Order
Suite Teardown      Accessibility Test Teardown
Test Timeout        ${TEST_TIMEOUT}

Force Tags          accessibility    patient-web    wcag-aa

*** Test Cases ***
Patient Login Cookies Modal Accessibility
    [Documentation]    Test accessibility of patient login page when cookies modal is open
    [Tags]    login    wcag-aa
    [Setup]    Open Browser    ${PATIENT_LOGIN_URL}    ${BROWSER}
    [Teardown]    Close Browser

    Check That Cookie Settings Are Displayed
    ${violations}=    Run Accessibility Tests    Cookies Modal

Patient Login Page Accessibility
    [Documentation]    Test accessibility of patient login page
    [Tags]    login    wcag-aa
    [Setup]    Open Browser    ${PATIENT_LOGIN_URL}    ${BROWSER}
    [Teardown]    Close Browser

    Accept All Cookies If Visible
    Wait Until Element Is Visible    ${landing_page_login_button}

    ${violations}=    Run Accessibility Tests    Patient Login Page
    

