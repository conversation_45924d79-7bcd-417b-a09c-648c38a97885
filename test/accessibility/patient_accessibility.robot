*** Settings ***
Documentation       Accessibility tests for patient app
...                 Tests WCAG 2.1 AA compliance across patient workflows

Resource            ${EXECDIR}${/}resources${/}accessibility.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource

Suite Setup         Run Keywords    Set Libraries Order
Suite Teardown      Accessibility Test Teardown
Test Timeout        ${TEST_TIMEOUT}

Force Tags          accessibility    patient-web    wcag-aa

*** Test Cases ***
Patient Login Accessibility
    [Documentation]    Test accessibility of patient login workflow
    [Tags]    login    wcag-aa

    Open Browser    ${PATIENT_LOGIN_URL}    ${BROWSER}
    Set Window Size    ${WINDOW_WIDTH}    ${WINDOW_HEIGHT}

    ${violations}=    Run All Accessibility Tests

    [Teardown]    Close Browser
